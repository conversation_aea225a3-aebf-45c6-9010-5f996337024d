

-- access-office created by <PERSON><PERSON><PERSON>
Config.DoorList['access-office'] = {
    locked = true,
    doorRate = 1.0,
    doors = {
        {objName = 1109357065, objYaw = 70.000213623047, objCoords = vec3(273.183899, 216.862823, 110.280518)},
        {objName = 1109357065, objYaw = 250.00028991699, objCoords = vec3(272.472107, 214.907272, 110.280518)}
    },
    audioRemote = false,
    doorType = 'double',
    pickable = false,
    distance = 2,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- right-access created by <PERSON>ri<PERSON>
Config.DoorList['right-access'] = {
    locked = true,
    objYaw = 340.00024414063,
    objCoords = vec3(272.642151, 219.898712, 97.317978),
    objName = 409280169,
    pickable = false,
    fixText = false,
    doorRate = 1.0,
    doorType = 'door',
    distance = 2,
    audioRemote = false,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- left-access created by Cribbe
Config.DoorList['left-access'] = {
    locked = true,
    objYaw = 340.00024414063,
    objCoords = vec3(270.103210, 212.922928, 97.317978),
    objName = 409280169,
    pickable = false,
    fixText = false,
    doorRate = 1.0,
    doorType = 'door',
    distance = 2,
    audioRemote = false,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- right-saferoom created by Cribbe
Config.DoorList['right-saferoom'] = {
    objCoords = vec3(250.564209, 233.399384, 97.317978),
    doorRate = 1.0,
    objName = 409280169,
    audioRemote = false,
    objYaw = 340.00024414063,
    fixText = false,
    doorType = 'door',
    locked = true,
    distance = 2,
    pickable = false,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- left-saferoom created by Cribbe
Config.DoorList['left-saferoom'] = {
    objCoords = vec3(244.558014, 216.897278, 97.317978),
    doorRate = 1.0,
    objName = 409280169,
    audioRemote = false,
    objYaw = 340.00024414063,
    fixText = false,
    doorType = 'door',
    locked = true,
    distance = 2,
    pickable = false,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- vault created by Cribbe
Config.DoorList['vault'] = {
    objName = 961976194,
    objCoords = vec3(234.985733, 228.069611, 97.721848),
    locked = true,
    pickable = false,
    distance = 2,
    objYaw = 70.000213623047,
    fixText = false,
    doorRate = 1.0,
    doorType = 'door',
    audioRemote = false,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- cellar-thermite-right created by Cribbe
Config.DoorList['cellar-thermite-right'] = {
    doorType = 'door',
    objName = 409280169,
    fixText = false,
    objCoords = vec3(256.412445, 229.275940, 97.317978),
    distance = 2,
    doorRate = 1.0,
    audioRemote = false,
    locked = true,
    pickable = false,
    objYaw = 70.000221252441,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- cellar-thermite-left created by Cribbe
Config.DoorList['cellar-thermite-left'] = {
    doorType = 'door',
    objName = 409280169,
    fixText = false,
    objCoords = vec3(251.649841, 216.190613, 97.317978),
    distance = 2,
    doorRate = 1.0,
    audioRemote = false,
    locked = true,
    pickable = false,
    objYaw = 250.00028991699,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- lobby1 created by Cribbe
Config.DoorList['lobby1'] = {
    doorType = 'door',
    objName = -2121568016,
    fixText = false,
    objCoords = vec3(256.606812, 229.689621, 106.370247),
    distance = 2,
    doorRate = 1.0,
    audioRemote = false,
    locked = true,
    pickable = false,
    objYaw = 70.000221252441,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- lobby2 created by Cribbe
Config.DoorList['lobby2'] = {
    doorType = 'door',
    objName = -2121568016,
    fixText = false,
    objCoords = vec3(270.230652, 221.267303, 106.370247),
    distance = 2,
    doorRate = 1.0,
    audioRemote = false,
    locked = true,
    pickable = false,
    objYaw = 70.000221252441,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- lobby3 created by Cribbe
Config.DoorList['lobby3'] = {
    doorType = 'door',
    objName = -2121568016,
    fixText = false,
    objCoords = vec3(267.369965, 213.407990, 106.370247),
    distance = 2,
    doorRate = 1.0,
    audioRemote = false,
    locked = true,
    pickable = false,
    objYaw = 250.00028991699,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- lobby4 created by Cribbe
Config.DoorList['lobby4'] = {
    doorType = 'door',
    objName = -2121568016,
    fixText = false,
    objCoords = vec3(251.519867, 215.713150, 106.370247),
    distance = 2,
    doorRate = 1.0,
    audioRemote = false,
    locked = true,
    pickable = false,
    objYaw = 250.00028991699,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- backdoor created by Cribbe
Config.DoorList['backdoor'] = {
    doorType = 'door',
    objName = 267980221,
    fixText = false,
    objCoords = vec3(272.790466, 206.480453, 106.382172),
    distance = 2,
    doorRate = 1.0,
    audioRemote = false,
    locked = true,
    pickable = false,
    objYaw = 340.00024414063,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- stairs-door created by Cribbe
Config.DoorList['stairs-door'] = {
    doorType = 'door',
    objName = 267980221,
    fixText = false,
    objCoords = vec3(277.595337, 223.541580, 106.380432),
    distance = 2,
    doorRate = 1.0,
    audioRemote = false,
    locked = true,
    pickable = false,
    objYaw = 160.00022888184,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- vault-leftdoor created by Cribbe
Config.DoorList['vault-leftdoor'] = {
    objYaw = 340.00024414063,
    distance = 2,
    objName = 643152522,
    fixText = false,
    objCoords = vec3(229.890533, 227.342010, 97.323975),
    doorType = 'door',
    audioRemote = false,
    doorRate = 1.0,
    pickable = false,
    locked = true,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- vault-rightdoor created by Cribbe
Config.DoorList['vault-rightdoor'] = {
    objYaw = 160.00025939941,
    distance = 2,
    objName = 643152522,
    fixText = false,
    objCoords = vec3(225.646286, 228.886780, 97.323975),
    doorType = 'door',
    audioRemote = false,
    doorRate = 1.0,
    pickable = false,
    locked = true,
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}

-- cellar-extraroom created by Cribbe
Config.DoorList['cellar-extraroom'] = {
    objCoords = vec3(265.779083, 225.866806, 97.317978),
    audioRemote = false,
    pickable = false,
    doorRate = 1.0,
    fixText = false,
    objYaw = 70.000221252441,
    distance = 2,
    objName = 409280169,
    locked = true,
    doorType = 'door',
    --audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
    --audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
    --autoLock = 1000,
}