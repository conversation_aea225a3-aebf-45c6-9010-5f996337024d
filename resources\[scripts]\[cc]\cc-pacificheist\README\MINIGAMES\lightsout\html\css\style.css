body {
  font-size: 1.5em;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

a,
a:link,
a:visited,
a:hover,
a:active {
  text-decoration: none;
  color: #f1f1f1;
  font-size: 0.8em;
}

#lights-out {
  display: none;
  padding: 0.3vh;
  background: #232832;
  margin-top: 30vh;
  transform: scalex(2.0) scaley(2.01) translate(1px, 8px);
  font-family: sans-serif;
  font-weight: 300;
  font-size: 15px;
  align-items: center;
}

#lights-out > table {
  table-layout: fixed;
  border-spacing: 5px;
}

#lights-out > table td {
  padding: 0;
  margin: 0;
  min-width: 4em;
  width: 4em;
  min-height: 4em;
  height: 4em;
}

@keyframes lights-on {
  from { background-color: #0B4E1A; }
  to   { background-color: #3eed57; }
}

@keyframes lights-off {
  from { background-color: #3eed57; }
  to   { background-color: #0B4E1A; }
}

#lights-out > table td.fail {
  background-color: #f51911;
  animation: lights-on 0.2s;
}

#lights-out > table td.on {
  background-color: #3eed57;
  animation: lights-on 0.2s;
}

#lights-out > table td.off {
  background-color: #0B4E1A;
  animation: lights-off 0.2s;
}

#instructions {
  background: #232832;
  opacity: 0.9;
  color: #f1f1f1;
  border: 1px solid #f1f1f1;
  position: absolute;
  padding: 1em;
  max-width: 450px;
  display: none;
}