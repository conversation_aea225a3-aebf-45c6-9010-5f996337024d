/* :root {
    --block-color: #394D61;
    --bg-color: #16181C;
  } */

.container {
    position: relative;
    top: 20vh;
    display:none;
}
  /* body{
    background-color: var(--bg-color);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  } */
  div.grid{
    display: none;
    margin: 20px auto;
    width: 600px;
    height: 600px;
    background: #2D3139;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr 1fr 1fr 1fr;
    gap: 20px;
    padding: 10px;
  }
  .grid.won .block{
    background: #2a8040;
  }
  .grid.lost .block{
    background: #F22300;
  }
  
  .block{
    background: #394D61;
    width: 100%;
    height: 100%;
    border-radius: 1px;
    cursor: pointer;
  }
  .block.show,.block.correct{
    background: #bfbfbf;
  }
  .block.incorrect{
    background: #F22300;
  }
  .grid.lost .show.block {
    background: #6d9070;
  }
  .grid.lost .block {
    background: #bd5151;
  }
  
  .grid.lost .block.clicked.incorrect {
    background: #F22300;
  }
  .grid.lost .block.clicked.correct {
    background: #bfbfbf;
  }
  

  
  