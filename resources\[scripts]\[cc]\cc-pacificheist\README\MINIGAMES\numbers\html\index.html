<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="css/style.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>	
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,200,300,400,500,600,700" rel="stylesheet">
  <script type="text/javascript" src="js/scripts.js" async></script>
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/solid.css" crossorigin="anonymous">
</head>

<div class="minigame">
  <div class="splash"><div class="fa hacker"></div>Input password as shown</div>
  <div class="numbers hidden"></div>
  <div class="input hidden">
      <div class="answer">
          <div class="answer-wrapper">
              <label for="answer">Password</label>
              <input id="answer" type="text" autocomplete="off" autofocus="" value="" maxlength="24">
          </div>
      </div>
      <div class="solution"></div>
  </div>
</div>

<!-- This is a test button in case you want to be able to test in browser! -->
<!-- <button style="font-family: Roboto; font-weight: 500; background-color: #232832; color: white; transform: translate(40px, 10px) scale(1.6); border-radius: 2px;" onclick="StartNumbersHack(24, 1)"> Start a test hack! </button> -->